2.6.0
- 增加屏蔽强度设置
- 增加白名单设置
- 增加更多工具

2.7.0
- 优化屏蔽强度设置（提示用户屏蔽强度逻辑）
- 优化语言切换（或移除）
- 部分功能限制付费用户
- 增加VIP徽章
- 新增收款功能。gumroad.com
- 参考chrome-extension://ljfjnlcnpmabfcgcmffkmgainghokdpl/dashboard.html
- https://burningvocabulary.com/price （该插件就是使用gumroad收款）


      // 判断激活码是否有效，是否是pro用户
      async function verifyLicense() {
        // 测试用的激活码和产品ID
        const testLicenseKey = "D6F3F923-908C4DCE-BBB60779-90810FAE";
        const testProductId = "frojl";
        
        try {
          const response = await fetch('https://api.gumroad.com/v2/licenses/verify', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `product_id=${testProductId}&license_key=${testLicenseKey}`
          });

          const data = await response.json();
          
          // 验证成功且未退款/取消订阅
          if (data.success && 
              !data.purchase.refunded && 
              !data.purchase.chargebacked &&
              !data.purchase.subscription_ended_at && 
              !data.purchase.subscription_cancelled_at) {
            return true; // 是pro用户
          }
          return false; // 不是pro用户
          
        } catch (error) {
          console.error('验证激活码失败:', error);
          return false;
        }
      }