<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Hide Images|Videos</title>
    <script src="js/jquery-3.7.0.min.js"></script>
    <script src="js/popup.js" type="module"></script>
    <link rel="stylesheet" href="css/tailwind.min.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 添加以下 CSS */
        .truncate {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 200px; /* 根据需要调整宽度 */
        }
        .tooltip {
            white-space: nowrap;
        }
    </style>
  </head>
  <body class="bg-gray-100 text-gray-800 select-none p-4 popup">
    <div class="max-w-sm mx-auto bg-white rounded-lg shadow-md overflow-hidden">
      <div class="px-4 py-3 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
        <h2 class="text-lg font-semibold text-gray-700 truncate relative group" title="__MSG_appName__">
          __MSG_appName__
          <span class="tooltip opacity-0 group-hover:opacity-100 transition-opacity duration-300 absolute left-0 top-full mt-2 p-2 bg-gray-800 text-white text-sm rounded shadow-lg z-10">__MSG_appName__</span>
        </h2>
        <div class="relative" id="moreBtn">
          <button class="p-1 rounded-full hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            <svg class="h-6 w-6 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
            </svg>
          </button>
          <div id="moreMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 hidden">
            <a href="https://chromewebstore.google.com/detail/lnemmogegmgllangfmlpclaomcknfnbp" target="_blank" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">__MSG_leaveReview__</a>
            <a href="https://ko-fi.com/xzsoft" target="_blank" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">__MSG_donate__</a>
            <a href="mailto:<EMAIL>" target="_blank" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">__MSG_suggestionsAdvice__</a>
            <a href="https://xzonesoft.com" target="_blank" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">About</a>
          </div>
        </div>
      </div>
      <!-- 更新日志通知样式 -->
      <div class="flex justify-center items-center mt-4 relative text-indigo-700 bg-indigo-100 ml-2 mr-2" update-log-version="3.2.0">
        <div id="updateLog" class="relative bg-violet-100 p-2 pr-8 shadow-sm border border-violet-200 w-full">
          <button class="absolute right-2 top-3 text-violet-500 hover:text-violet-700 transition-colors" id="closeUpdateLog" version="3.2.0">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
          <div class="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="flex-shrink-0 h-4 w-4 text-violet-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            <div class="overflow-hidden whitespace-nowrap">
              <div class="animate-scroll">
                <div class="text-sm text-violet-700 font-medium">
                  V3.2.0 (2025-06-08) : Added blacklist function/新增黑名单功能/ブラックリスト機能を追加
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="p-4 space-y-6">
        <label class="flex items-center justify-between">
          <span class="text-sm font-medium text-gray-700">__MSG_noGraphMode__</span>
          <div class="relative inline-block w-10 align-middle select-none transition duration-200 ease-in">
            <input type="checkbox" id="image" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"/>
            <label for="image" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
          </div>
        </label>
        <label class="flex items-center justify-between hidden" id="title">
          <span class="text-sm font-medium text-gray-700">title</span>
          <div class="relative inline-block w-10 align-middle select-none transition duration-200 ease-in">
            <input type="checkbox" inp="title" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"/>
            <label for="title" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
          </div>
        </label>
        <label class="flex items-center justify-between hidden" id="favicon">
          <span class="text-sm font-medium text-gray-700">favicon</span>
          <div class="relative inline-block w-10 align-middle select-none transition duration-200 ease-in">
            <input type="checkbox" inp="favicon" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"/>
            <label for="favicon" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
          </div>
        </label>
        <!-- 黑白名单模式选择 -->
        <div id="listModeSection" class="space-y-3">
          <label class="flex items-center justify-between">
            <span class="text-sm font-medium text-gray-700">__MSG_listMode__</span>
            <div class="inline-flex bg-gray-100 rounded-lg overflow-hidden">
              <button id="whitelistModeBtn" class="px-3 py-1 text-xs bg-violet-400 text-white hover:bg-violet-500 transition-colors">
                __MSG_whitelistMode__
              </button>
              <button id="blacklistModeBtn" class="px-3 py-1 text-xs hover:bg-gray-200 transition-colors">
                __MSG_blacklistMode__
              </button>
            </div>
          </label>

          <!-- 白名单选项 -->
          <label id="whitelistOption" class="flex items-center justify-between">
            <span class="text-sm font-medium text-gray-700">__MSG_addToWhiteList__</span>
            <div class="relative inline-block w-10 align-middle select-none transition duration-200 ease-in">
              <input type="checkbox" id="enable" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"/>
              <label for="enable" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
            </div>
          </label>

          <!-- 黑名单选项 -->
          <label id="blacklistOption" class="flex items-center justify-between hidden">
            <span class="text-sm font-medium text-gray-700">__MSG_addToBlacklist__</span>
            <div class="relative inline-block w-10 align-middle select-none transition duration-200 ease-in">
              <input type="checkbox" id="enableBlacklist" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"/>
              <label for="enableBlacklist" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
            </div>
          </label>
        </div>
        <!-- 屏蔽强度 -->
        <label class="flex items-center justify-between">
          <span class="text-sm font-medium text-gray-700">__MSG_blockStrength__</span>
          <div class="inline-flex bg-gray-100 rounded-lg overflow-hidden">
            <button class="w-14 py-1 text-[11px] hover:bg-gray-200 transition-colors" data-strength="low">
              __MSG_blockStrengthLow__
            </button>
            <button class="w-14 py-1 text-[11px] bg-violet-400 text-white hover:bg-violet-500 transition-colors" data-strength="medium">
              __MSG_blockStrengthMedium__  
            </button>
            <button class="relative w-14 py-1 text-[11px] hover:bg-gray-200 transition-colors group" data-strength="high">
              <span class="pro-badge text-[9px]">Pro</span>
              __MSG_blockStrengthHigh__
            </button>
          </div>
        </label>
        <button id="exclude" class="w-full flex justify-between items-center px-2 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
          <span>__MSG_Settings__</span>
          <svg class="h-5 w-5 text-indigo-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
          </svg>
        </button>
        <!-- 添加星级评分系统 -->
        <div class="flex justify-center items-center mt-4 relative">
          <div class="star-rating">
            <span class="star" data-rating="1">&#9733;</span>
            <span class="star" data-rating="2">&#9733;</span>
            <span class="star" data-rating="3">&#9733;</span>
            <span class="star" data-rating="4">&#9733;</span>
            <span class="star" data-rating="5">&#9733;</span>
          </div>
          <div class="rating-emoji absolute -top-8 left-1/2 transform -translate-x-1/2 opacity-0 transition-opacity duration-200">
            <span class="emoji text-2xl"></span>
          </div>
        </div>
      </div>
    </div>
    <script src="js/i18n.js"></script>
    <div id="upgradeModal" class="modal hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full">
      <div class="relative top-10 mx-auto p-5 border w-72 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-violet-100">
            <svg class="h-6 w-6 text-violet-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
          <h3 class="text-lg leading-6 font-medium text-gray-900">__MSG_upgradeTip__</h3>
          <div class="mt-2 px-7 py-3">
            <p class="text-sm text-gray-500">
              __MSG_upgradeTipDesc__
            </p>
          </div>
          <div class="items-center py-3">
            <button id="upgradeBtn" class="px-4 py-2 bg-violet-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-violet-600 focus:outline-none focus:ring-2 focus:ring-violet-300">
              __MSG_upgradeNow__
            </button>
            <button id="cancelUpgradeBtn" class="ml-3 px-4 py-2 bg-gray-100 text-gray-700 text-base font-medium rounded-md shadow-sm hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300">
              __MSG_cancel__
            </button>
          </div>
          <div class="mt-3 border-t pt-3">
            <button id="verifyLicenseBtn" class="text-sm text-violet-600 hover:text-violet-700">
              __MSG_verifyLicense__
            </button>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>