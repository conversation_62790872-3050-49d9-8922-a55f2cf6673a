<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>黑白名单功能测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-image {
            width: 200px;
            height: 150px;
            margin: 10px;
            border: 1px solid #ccc;
        }
        .instructions {
            background-color: #f0f8ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background-color: #f9f9f9;
            border-left: 4px solid #007cba;
        }
    </style>
</head>
<body>
    <h1>黑白名单功能测试页面</h1>
    
    <div class="instructions">
        <h2>测试说明</h2>
        <p>这个页面用于测试Chrome扩展的黑白名单功能。请按照以下步骤进行测试：</p>
        
        <div class="step">
            <strong>步骤1：</strong> 确保扩展已安装并启用
        </div>
        
        <div class="step">
            <strong>步骤2：</strong> 点击扩展图标，开启"无图模式"
        </div>
        
        <div class="step">
            <strong>步骤3：</strong> 测试白名单模式
            <ul>
                <li>选择"白名单模式"</li>
                <li>不添加当前网站到白名单 - 图片应该被隐藏</li>
                <li>添加当前网站到白名单 - 图片应该显示</li>
            </ul>
        </div>
        
        <div class="step">
            <strong>步骤4：</strong> 测试黑名单模式
            <ul>
                <li>选择"黑名单模式"</li>
                <li>不添加当前网站到黑名单 - 图片应该显示</li>
                <li>添加当前网站到黑名单 - 图片应该被隐藏</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h3>测试图片</h3>
        <p>以下图片用于测试隐藏功能：</p>
        
        <img src="https://via.placeholder.com/200x150/FF6B6B/FFFFFF?text=Test+Image+1" 
             alt="测试图片1" class="test-image">
        
        <img src="https://via.placeholder.com/200x150/4ECDC4/FFFFFF?text=Test+Image+2" 
             alt="测试图片2" class="test-image">
        
        <img src="https://via.placeholder.com/200x150/45B7D1/FFFFFF?text=Test+Image+3" 
             alt="测试图片3" class="test-image">
        
        <img src="https://via.placeholder.com/200x150/96CEB4/FFFFFF?text=Test+Image+4" 
             alt="测试图片4" class="test-image">
    </div>

    <div class="test-section">
        <h3>测试视频</h3>
        <video width="320" height="240" controls>
            <source src="https://www.w3schools.com/html/mov_bbb.mp4" type="video/mp4">
            您的浏览器不支持视频标签。
        </video>
    </div>

    <div class="test-section">
        <h3>背景图片测试</h3>
        <div style="width: 200px; height: 150px; background-image: url('https://via.placeholder.com/200x150/FFA07A/FFFFFF?text=Background'); background-size: cover; border: 1px solid #ccc; margin: 10px;">
            <p style="padding: 20px; color: white; text-shadow: 1px 1px 2px black;">背景图片测试</p>
        </div>
    </div>

    <div class="test-section">
        <h3>预期行为</h3>
        <ul>
            <li><strong>白名单模式：</strong>只有在白名单中的网站显示图片，其他网站隐藏图片</li>
            <li><strong>黑名单模式：</strong>在黑名单中的网站隐藏图片，其他网站正常显示图片</li>
            <li><strong>模式切换：</strong>在popup中可以轻松切换白名单和黑名单模式</li>
            <li><strong>网站管理：</strong>在options页面可以管理白名单和黑名单</li>
        </ul>
    </div>

    <script>
        // 添加一些动态内容用于测试
        console.log('测试页面已加载');
        
        // 检测扩展是否工作
        setTimeout(() => {
            const images = document.querySelectorAll('img');
            let hiddenCount = 0;
            images.forEach(img => {
                if (img.style.visibility === 'hidden') {
                    hiddenCount++;
                }
            });
            
            if (hiddenCount > 0) {
                console.log(`检测到 ${hiddenCount} 张图片被隐藏，扩展正在工作`);
            } else {
                console.log('没有检测到图片被隐藏，请检查扩展设置');
            }
        }, 2000);
    </script>
</body>
</html>
