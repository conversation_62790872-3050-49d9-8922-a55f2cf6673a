/*common*/
body {
    font-family: "Helvetica Neue", "Helvetica", "Arial", "sans-serif";
    padding: 10px 15px;
}
/* 优化滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background-color: #f3f4f6;  /* 添加浅灰色背景 */
}

::-webkit-scrollbar-thumb {
    background-color: #bea9ff;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background-color: #af8bfc;  /* 悬停时稍微深一点的紫色 */
}
.show {
    display: block !important;
}
.d-none {
    display: none;
}
/*popup*/
body.popup {
    min-width: 350px;
    width: 350px;
    overflow: hidden;
    /* padding: 6px 5px;
    width: 250px;
    padding-bottom: 20px;
    box-sizing: border-box; */
}

body .main {
    height: 160px;
    border-radius: 5px;
    background: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    padding: 10px;
    /* display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center; */
    width: 100%;
    box-sizing: border-box;
}

.popup .switch-group {
    margin-top: 8px;
    /* border: 1px solid #2196f3; */
    padding: 5px 3px;
    border-radius: 34px;
    display: block;
    cursor: pointer;
}

.popup .switch-group:first-of-type {
    margin-top: 0;
}

.popup .switch {
    position: relative;
    display: inline-block;
    width: 48px;
    height: 24px;
    line-height: 24px;
    left: 170px;
}

.popup .switch .label {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    /* color: #2196f3; */
    font-size: 15px;
    position: relative;
    display: inline-block;
    left: -170px;
    width: 170px;
}

.popup .switch input {
    display: none;
}

.popup .switch .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #2196f3;
}

.popup .switch .slider.round {
    border-radius: 34px
}

.popup .slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    right: 2px;
    bottom: 2px;
    background-color: white;
}

.popup .switch .slider.round:before {
    border-radius: 50%;
}

.popup .switch input:checked+.slider {
    background-color: #ccc;
}

.popup .switch input:focus+.slider {
    box-shadow: 0 0 1px #2196f3;
}

.popup .switch input:checked+.slider:before {
    -webkit-transform: translateX(-24px);
    -ms-transform: translateX(-24px);
    transform: translateX(-24px)
}
.popup #enable .switch .slider {
    background-color: #ccc;
}
.popup #enable .switch input:checked+.slider {
    background-color: #fff;
}
.popup #enable .slider:before {
    left: 2px;
}
.popup #enable .switch input:checked+.slider {
    background-color: #2196f3;
}
.popup #enable .switch input:checked+.slider:before {
    -webkit-transform: translateX(24px);
    -ms-transform: translateX(24px);
    transform: translateX(24px);
}

.popup #exclude .switch .label {
    font-variant: small-caps;
}
.popup #exclude {
    cursor: pointer;
}
.popup #exclude:hover {
    background: rgba(0,0,0,0.04);
}
/*exclude*/
.exclude #fileInput {
    display: none;
}
.table {
    border-spacing: 0;
    border-collapse: collapse;
    width: 500px;
    max-width: 100%;
    margin-bottom: 20px;
}
.table-bordered {
    border: 1px solid #ddd;
}
.table-bordered td, .table-bordered th {
    border: 1px solid #ddd;
    height: 35px;
    line-height: 35px;
    text-align: center;
}
.table button,
#add,
.export button {
    min-width: 5.14em;
    padding: 6px 12px;
    background-color: #fff;
    border: 1px solid rgb(218,220,224);
    border-radius: 4px;
    cursor: pointer;
}
.table .btn-danger {
    color: rgb(197,57,41);
}
#add {
    color: #2196f3;
    margin-left: 15px;
}
.table .btn-danger:hover {
    background: rgba(97,57,41,0.04);
    border-color: rgb(210, 227, 252);
}
#add:hover,.export button:hover {
    background: rgba(66,133,244,0.04);
    border-color: rgb(210, 227, 252);
}
.add-form {
    display: flex;
}
.add-form input {
    display: block;
    width: 400px;
    height: 28px;
    padding: 3px 6px;
    color: #555;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 4px;
}
hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
}
.header {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin-bottom: 19px;
    justify-content: space-between;
}
/* header-left */
.header-left {
    display: flex;
    flex-direction: row;
    align-items: center;
}
.header h2 {
    margin: 0 5px 0 10px;
    font-size: larger;
    font-size: 20px;
    color: #409EFF;
}
.header .version {
    color: #555;
    font-size: 14px;
}
/* header-right */
.more {
    width: 40px;
    height: 40px;
}
.more-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #fff;
}
.more-btn:hover {
    background-color: #eee;
    cursor: pointer;
    transition: all 0.2s;
}
.more-btn-icon {
    width: 20px;
    height: 20px;
    background: url('../img/more.png') no-repeat; /* 添加了分号 */
    background-size: cover;
}
.more-menu {
    position: absolute;
    top: 55px;
    right: 10px;
    min-width: 120px;
    /* box-shadow: 0 0 10px rgba(0, 0, 0, 0.2); */
    box-shadow: 0px 0px 35px 0px rgba(154, 161, 171, 0.15);
    z-index: 999;
    display: none;
    list-style: none;
    padding: 0.25rem 0;
    margin: 0.125rem 0 0;
    color: #6c757d;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #d9e3e9;
    border-radius: 0.25rem;
}
.more-menu-item {
    /* display: flex; */
    height: 25px;
    line-height: 25px;
    width: 100%;
    align-items: center;
    padding: 0 0.5rem;
    font-size: 14px;
    color: #6c757d;
    cursor: pointer;
    box-sizing: border-box;
}
.more-menu-item:hover,
.more-menu-item:focus {
    color: #272e37;
    text-decoration: none;
    background-color: #f8f9fa;
}
.more-menu-item a {
    text-decoration: none;
    color: inherit;
    outline: none;
    transition: none;
    display: inline-block;
    width: 100%;
}

/* a:hover,
a:active {
    text-decoration: none;
    color: inherit;
} */
.toggle-checkbox:checked {
    right: 0;
    border-color: #a78bfa; /* 更改为紫色 */
}
.toggle-checkbox:checked + .toggle-label {
    background-color: #a78bfa; /* 更改为紫色 */
}

/* 星级评分系统样式 */
.star-rating {
  font-size: 24px;
  color: #ccc;
  cursor: pointer;
}

.star-rating .star {
  transition: color 0.2s ease-in-out;
}

.star-rating .star:hover,
.star-rating .star.active {
  color: #ffd700;
}

.star-rating .star:hover ~ .star {
  color: #ccc;
}

.rating-emoji {
  pointer-events: none;
}

.rating-emoji.show {
  opacity: 1 !important;
}

.rating-emoji .emoji {
  display: inline-block;
  transform-origin: center;
  animation: bounceIn 0.3s ease-out;
}

@keyframes bounceIn {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* ========== options page start ========== */

/* switch toggle style */
.switch-toggle {
    width: 44px;
    height: 24px;
    background-color: #ccc;
    border-radius: 12px;
    padding: 2px;
    transition: background-color 0.3s;
    cursor: pointer;
    position: relative;
}

.switch-toggle::after {
    content: '';
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: white;
    position: absolute;
    left: 2px;
    transition: transform 0.3s;
}

input:checked + .switch-toggle {
    background-color: #2196f3;
}

input:checked + .switch-toggle::after {
    transform: translateX(20px);
}

.menu-item {
    color: #4B5563;  /* 默认颜色 */
    transition: all 0.2s;
}

.menu-item.active {
    background-color: #EBF5FF;
    color: #3B82F6;
    font-weight: 500;
}

.menu-item:hover {
    background-color: #F3F4F6;
    color: #3B82F6;  /* 悬停时改变字体颜色 */
}

.page {
    display: none;
}

.page.active {
    display: block;
}

.app-description {
    min-height: 60px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
/* ========== options page end ========== */

/* 紫色主题相关样式 */
.bg-violet-400 {
    background-color: #a78bfa;
}

.bg-violet-500 {
    background-color: #8b5cf6;
}

.hover\:bg-violet-500:hover {
    background-color: #8b5cf6;
}

.hover\:bg-violet-400:hover {
    background-color: #a78bfa;
}

/* 菜单项样式 */
.menu-item.active {
    background-color: #a78bfa;
    color: white;
}

.menu-item:hover {
    background-color: #f3f4f6;
}

.menu-item:hover:not(.active) {
    color: #8b5cf6;
}

/* 开关按钮样式 */
.switch-toggle {
    width: 3rem;
    height: 1.5rem;
    background-color: #e5e7eb;
    border-radius: 9999px;
    position: relative;
    transition: background-color 0.2s;
}

.switch-toggle::after {
    content: "";
    position: absolute;
    left: 0.25rem;
    top: 0.25rem;
    width: 1rem;
    height: 1rem;
    background-color: white;
    border-radius: 50%;
    transition: transform 0.2s;
}

input:checked + .switch-toggle {
    background-color: #a78bfa;
}

input:checked + .switch-toggle::after {
    transform: translateX(1.5rem);
}

/* 输入框焦点状态 */
input:focus, select:focus {
    border-color: #a78bfa;
    box-shadow: 0 0 0 2px rgba(167, 139, 250, 0.2);
}

/* 按钮样式 */
.btn-primary {
    background-color: #a78bfa;
}

.btn-primary:hover {
    background-color: #8b5cf6;
}

/* 深色模式样式 */
body.dark {
    background-color: #1a1a1a;
    color: #ffffff;
}

body.dark .bg-white {
    background-color: #2d2d2d;
}

body.dark .bg-gray-50 {
    background-color: #383838;
}

body.dark .bg-gray-100 {
    background-color: #1a1a1a;
}

body.dark .text-gray-500,
body.dark .text-gray-600,
body.dark .text-gray-700 {
    color: #d0d0d0;
}

body.dark .border,
body.dark .border-t,
body.dark .border-b {
    border-color: #404040;
}

body.dark .menu-item {
    color: #e0e0e0;
}

body.dark .menu-item:hover {
    background-color: #383838;
}

body.dark .menu-item.active {
    background-color: #a78bfa;
    color: #ffffff;
}

body.dark input,
body.dark select {
    background-color: #383838;
    border-color: #404040;
    color: #ffffff;
}

body.dark .hover\:bg-gray-100:hover {
    background-color: #383838;
}

body.dark .hover\:bg-gray-200:hover {
    background-color: #404040;
}

.pro-badge {
    position: absolute;
    top: 0;
    right: -4px;
    background: linear-gradient(to right, #f59e0b, #ea580c);
    color: white;
    padding: 0 8px;
    border-radius: 9999px;
    font-size: 0.6rem;
    font-weight: bold;
}

/* 底部PRO状态显示 */
.pro-status {
    /* background-color: #E5E7EB; */
    color: #6B7280;
    transition: all 0.2s;
    min-width: 80px;
    text-align: center;
}

.pro-status[data-is-pro="true"] {
    /* background-color: #8B5CF6; */
    color: white;
}

.pro-status[data-is-pro="false"] {
    display: none;
}

/* 升级PRO按钮样式 */
.upgrade-pro-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    background: linear-gradient(to right, #f59e0b, #ea580c);
    color: white;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s;
    text-decoration: none;
}

.upgrade-pro-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.upgrade-pro-btn svg {
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.5rem;
}

.pro-btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    background: linear-gradient(to right, #f59e0b, #ea580c);
    color: white;
    border-radius: 0.375rem;
    font-weight: 500;
    transition: background-color 0.2s;
  }
  
  .pro-btn:hover {
    background-color: #7c3aed;
  }

/* Hide scrollbar for Chrome, Safari and Opera */
.no-scrollbar::-webkit-scrollbar {
    display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.no-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}

.animate-scroll {
  display: inline-block;
  animation: scroll 30s linear infinite;
  padding-left: 100%;
  white-space: nowrap;
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}
