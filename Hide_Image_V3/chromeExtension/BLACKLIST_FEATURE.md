# 黑名单功能实现说明

## 功能概述

本次更新为Chrome扩展添加了黑名单功能，用户现在可以在白名单模式和黑名单模式之间切换：

- **白名单模式**：只有白名单中的网站显示图片，其他网站隐藏图片（原有功能）
- **黑名单模式**：黑名单中的网站隐藏图片，其他网站正常显示图片（新增功能）

## 实现的文件修改

### 1. 国际化文本 (i18n)
- `_locales/zh_CN/messages.json` - 添加中文文本
- `_locales/en/messages.json` - 添加英文文本

新增的文本键：
- `listMode` - 名单模式
- `whitelistMode` - 白名单模式  
- `blacklistMode` - 黑名单模式
- `addToBlacklist` - 添加到黑名单
- `blackList` - 黑名单列表
- `addBlacklist` - 新增黑名单
- `addWhiteList` - 新增白名单

### 2. 用户界面 (UI)

#### popup.html
- 添加黑白名单模式选择按钮
- 添加黑名单开关选项
- 根据选择的模式显示对应的选项

#### options.html  
- 添加黑名单管理页面
- 新增黑名单菜单项
- 黑名单域名添加和删除功能

### 3. 逻辑实现

#### popup.js
- 新增 `initListMode()` 函数初始化黑白名单模式
- 添加模式切换按钮事件处理
- 添加黑名单开关事件处理
- 修改 `syncUI()` 函数支持模式切换

#### options.js
- 新增 `initBlacklistManagement()` 函数
- 新增 `updateBlacklistDomainsList()` 函数
- 添加黑名单域名的增删功能

#### content.js
- 修改图片隐藏逻辑支持黑名单模式
- 根据 `listMode` 判断使用白名单还是黑名单逻辑

#### background.js
- 初始化时设置默认的黑白名单模式和空数组

## 数据存储结构

新增的存储字段：
- `listMode`: 'whitelist' | 'blacklist' - 当前使用的模式
- `blacklistDomains`: string[] - 黑名单域名数组

保持的存储字段：
- `domains`: string[] - 白名单域名数组（保持兼容性）
- `active`: boolean - 无图模式是否启用
- `hide`: string - 隐藏模式

## 功能逻辑

### 白名单模式 (原有逻辑)
```javascript
if (listMode === 'whitelist') {
  shouldHideImages = !domains.includes(currentDomain);
}
```

### 黑名单模式 (新增逻辑)
```javascript
if (listMode === 'blacklist') {
  shouldHideImages = blacklistDomains.includes(currentDomain);
}
```

## 用户体验

1. **无缝切换**：用户可以在popup中轻松切换白名单和黑名单模式
2. **直观显示**：根据选择的模式显示对应的添加选项
3. **独立管理**：白名单和黑名单在options页面分别管理
4. **向后兼容**：保持原有白名单功能不变

## 测试方法

1. 打开 `test.html` 测试页面
2. 启用扩展的无图模式
3. 测试白名单模式：
   - 不添加当前网站 → 图片应被隐藏
   - 添加当前网站 → 图片应显示
4. 测试黑名单模式：
   - 不添加当前网站 → 图片应显示
   - 添加当前网站 → 图片应被隐藏

## 注意事项

- 黑名单功能对所有用户开放，无需Pro版本
- 模式切换会立即生效并刷新当前页面
- 白名单和黑名单数据独立存储，互不影响
- 保持了原有功能的完整性和兼容性
