;(async () => {
    chrome.storage.local.get(['active', 'hide'], function(result) {
      const { active, hide } = result;
      if (active) {
        const observerTarget = document.querySelector('html');
        const observerOptions = {
          childList: true,
          subtree: true,
          attributes: true,
        };
        const observer = new MutationObserver(() =>
          hideOrBlurImages(hide)
        );
        observer.observe(observerTarget, observerOptions);
  
        function hideOrBlurImages(hide) {
          const images = document.querySelectorAll('img'); // Select all images
          const videos = document.querySelectorAll('video'); // Select all images
          let totalImage = 0;
          if (hide === 'HIDE') {
            chrome.storage.sync.get(['domains', 'blacklistDomains', 'listMode'], function (result) {
              var domains = result.domains || [];
              var blacklistDomains = result.blacklistDomains || [];
              var listMode = result.listMode || 'whitelist';
              // get current domain
              var currentDomain = window.location.hostname;

              // 判断是否需要隐藏图片
              var shouldHideImages = false;
              if (listMode === 'whitelist') {
                // 白名单模式：不在白名单中的网站隐藏图片
                shouldHideImages = !domains.includes(currentDomain);
              } else {
                // 黑名单模式：在黑名单中的网站隐藏图片
                shouldHideImages = blacklistDomains.includes(currentDomain);
              }

              if (shouldHideImages) {
                // 获取屏蔽强度
                chrome.storage.sync.get(['blockStrength'], function (result) {
                  var blockStrength = result.blockStrength || 'medium';
  
                  if (blockStrength === 'low') {
                    // 图片宽高小于250px的可以显示
                    images.forEach((image, i) => {
                      if (image.width < 250 && image.height < 250) {
                        image.style.visibility = 'visible'; // Add 'hidden' CSS property
                      }else{
                        image.style.visibility = 'hidden'; // Add 'hidden' CSS property
                      }
                      totalImage = i;
                    });
                    videos.forEach((video, i) => {
                      video.style.visibility = 'hidden'; // Add 'hidden' CSS property
                    });
  
                  }else if (blockStrength === 'medium') {
                    images.forEach((image, i) => {
                      image.style.visibility = 'hidden'; // Add 'hidden' CSS property
                      totalImage = i;
                    });
                    videos.forEach((video, i) => {
                      video.style.visibility = 'hidden'; // Add 'hidden' CSS property
                    });
                    
                    // 把background-images的圖片也隱藏（pro功能）暂时放开免费
                    const backgroundImages = document.querySelectorAll('[style*="background-image"]');
                    backgroundImages.forEach((backgroundImage, i) => {
                      backgroundImage.style.visibility = 'hidden'; // Add 'hidden' CSS property
                    });
  
                  }else if (blockStrength === 'high') {
  
                    images.forEach((image, i) => {
                      image.style.visibility = 'hidden'; // Add 'hidden' CSS property
                      totalImage = i;
                    });
                    videos.forEach((video, i) => {
                      video.style.visibility = 'hidden'; // Add 'hidden' CSS property
                    });
  
                    // 隐藏svg(pro功能)
                    const svgs = document.querySelectorAll('svg');
                    svgs.forEach((svg, i) => {
                      svg.style.visibility = 'hidden'; // Add 'hidden' CSS property
                    });
                    // 隐藏canvas(pro功能)
                    const canvas = document.querySelectorAll('canvas');
                    canvas.forEach((canvas, i) => {
                      canvas.style.visibility = 'hidden'; // Add 'hidden' CSS property
                    });
                    // 隐藏gif(pro功能)
                    const gifs = document.querySelectorAll('iframe[src*=".gif"]');
                    gifs.forEach((gif, i) => {
                      gif.style.visibility = 'hidden'; // Add 'hidden' CSS property
                    });
  
                  }
  
                });
              }
            });
          }
        }
      }
    });
  })();