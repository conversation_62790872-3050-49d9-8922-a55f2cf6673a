function locale_text(key, default_value) {
  var value = chrome.i18n.getMessage(key);
  if (!value) {
    return default_value;
  }
  return value;
}

function locale_text_content(text) {
  return text.replace(/__MSG_(\w+)__/g, function(match, key) {
    return locale_text(key, match);
  });
}

function locale_text_node(textNode) {
  textNode.nodeValue = locale_text_content(textNode.nodeValue);
}

function extract_document(e) {
  var childNodes = e.childNodes;
  for (var i = 0; i < childNodes.length; i++) {
    var c = childNodes[i];
    switch (c.nodeType) {
      case 1: // 元素节点
        // 处理title属性
        if (c.hasAttribute('title')) {
          var titleValue = c.getAttribute('title');
          c.setAttribute('title', locale_text_content(titleValue));
        }
        extract_document(c);
        break;
      case 3: // 文本节点
        locale_text_node(c);
        break;
    }
  }
}

function addEvent(obj, evtName, fnHand<PERSON>, useCapture) {
  if (obj.addEventListener) {
    obj.addEventListener(evtName, fnHandler, !!useCapture);
  } else if (obj.attachEvent) {
    obj.attachEvent('on' + evtName, fnHandler);
  } else {
    obj["on" + evtName] = fnHandler;
  }
}

addEvent(window, 'load', function() {
  extract_document(document.getElementsByTagName('html')[0]);
});
