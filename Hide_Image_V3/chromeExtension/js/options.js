document.addEventListener('DOMContentLoaded', function() {
    // 初始化深色模式
    initDarkMode();
    // 初始化设置
    initSettings();
    // 初始化页面切换
    initPageSwitch();
    // 初始化白名单管理
    initWhitelistManagement();
    // 初始化黑名单管理
    initBlacklistManagement();
    // 屏蔽强度按钮切换
    initStrengthButtons();
    // 初始化PRO状态
    initProStatus();
    // 判断是否显示许可证验证弹窗
    showLicenseModal();
    updateProBadge();
});

// 深色模式初始化和管理
function initDarkMode() {
    const darkModeToggle = document.getElementById('darkModeToggle');
    
    // 从 storage 中读取深色模式状态
    chrome.storage.sync.get(['darkMode'], function(result) {
        if (result.darkMode) {
            darkModeToggle.checked = true;
            document.body.classList.add('dark');
        }
    });
    
    // 监听深色模式开关变化
    darkModeToggle.addEventListener('change', function() {
        const isDarkMode = this.checked;
        
        // 保存状态到 storage
        chrome.storage.sync.set({
            darkMode: isDarkMode
        });
        
        // 更新页面样式
        if (isDarkMode) {
            document.body.classList.add('dark');
        } else {
            document.body.classList.remove('dark');
        }
    });
}

// 设置页面初始化和管理
function initSettings() {
    // 获取保存的设置
    chrome.storage.sync.get(['language'], function(result) {
        // 初始化语言选择
        const languageSelect = document.getElementById('languageSelect');
        languageSelect.value = result.language || chrome.i18n.getUILanguage() || 'en';
        
        // 监听语言选择变化
        languageSelect.addEventListener('change', function() {
            const language = this.value;
            chrome.storage.sync.set({ language: language }, function() {
                // 如果语言改变了，刷新页面以应用新语言
                if (language !== chrome.i18n.getUILanguage()) {
                    setTimeout(() => {
                        window.location.reload();
                    }, 500);
                }
            });
        });

        // 展示对应强度按钮
        chrome.storage.sync.get(['blockStrength'], function(result) {
            console.log('resultblock', result);
            const blockStrength = result.blockStrength || 'medium';
            const strengthButtons = document.querySelectorAll('[data-strength]');
            strengthButtons.forEach(button => {
                if (button.getAttribute('data-strength') === blockStrength) {
                    button.click();
                }
            });
        });
    });
}

// 页面切换逻辑
function initPageSwitch() {
    const menuItems = document.querySelectorAll('.menu-item');
    const pages = document.querySelectorAll('.page');
    
    menuItems.forEach(item => {
        item.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = item.getAttribute('href').substring(1);
            
            // 更新菜单项状态
            menuItems.forEach(i => i.classList.remove('active'));
            item.classList.add('active');
            
            // 更新页面显示
            pages.forEach(page => {
                if (page.id === targetId) {
                    page.classList.remove('hidden');
                    page.classList.add('active');
                } else {
                    page.classList.add('hidden');
                    page.classList.remove('active');
                }
            });
        });
    });
}

// 白名单管理
function initWhitelistManagement() {
    const addBtn = document.getElementById('add');
    let domains = [];

    // 加载白名单数据
    chrome.storage.sync.get(['domains'], function(result) {
        domains = result.domains || [];
        updateDomainsList(domains);
    });

    // 添加域名
    addBtn.addEventListener('click', async function() {
        const isPro = await isProUser();
        if (!isPro) {
            // 如果不是Pro用户，显示升级提示
            const licenseModal = document.getElementById('licenseModal');
            licenseModal.classList.remove('hidden');
            return;
        }

        // 以下是原有的添加白名单逻辑
        var domain = document.getElementById('domainInput').value;
        if (domain) {
            chrome.storage.sync.get(['domains'], function(result) {
                var domains = result.domains || [];
                if (!domains.includes(domain)) {
                    domains.push(domain);
                    chrome.storage.sync.set({
                        domains: domains
                    }, function() {
                        document.getElementById('domainInput').value = '';
                        updateDomainsList(domains); // 重新加载域名列表
                    });
                }
            });
        }
    });

    // 修改输入框的回车事件处理
    document.getElementById('domainInput').addEventListener('keyup', async function(event) {
        if (event.key === 'Enter') {
            const isPro = await isProUser();
            if (!isPro) {
                // 如果不是Pro用户，显示升级提示
                const licenseModal = document.getElementById('licenseModal');
                licenseModal.classList.remove('hidden');
                return;
            }

            // 以下是原有的添加白名单逻辑
            var domain = this.value;
            if (domain) {
                chrome.storage.sync.get(['domains'], function(result) {
                    var domains = result.domains || [];
                    if (!domains.includes(domain)) {
                        domains.push(domain);
                        chrome.storage.sync.set({
                            domains: domains
                        }, function() {
                            document.getElementById('domainInput').value = '';
                            loadDomains(); // 重新加载域名列表
                        });
                    }
                });
            }
        }
    });

}

// 黑名单管理
function initBlacklistManagement() {
    const addBlacklistBtn = document.getElementById('addBlacklist');
    let blacklistDomains = [];

    // 加载黑名单数据
    chrome.storage.sync.get(['blacklistDomains'], function(result) {
        blacklistDomains = result.blacklistDomains || [];
        updateBlacklistDomainsList(blacklistDomains);
    });

    // 添加黑名单域名
    addBlacklistBtn.addEventListener('click', function() {
        var domain = document.getElementById('blacklistDomainInput').value;
        if (domain) {
            chrome.storage.sync.get(['blacklistDomains'], function(result) {
                var blacklistDomains = result.blacklistDomains || [];
                if (!blacklistDomains.includes(domain)) {
                    blacklistDomains.push(domain);
                    chrome.storage.sync.set({
                        blacklistDomains: blacklistDomains
                    }, function() {
                        document.getElementById('blacklistDomainInput').value = '';
                        updateBlacklistDomainsList(blacklistDomains);
                    });
                }
            });
        }
    });

    // 黑名单输入框回车事件
    document.getElementById('blacklistDomainInput').addEventListener('keyup', function(event) {
        if (event.key === 'Enter') {
            var domain = this.value;
            if (domain) {
                chrome.storage.sync.get(['blacklistDomains'], function(result) {
                    var blacklistDomains = result.blacklistDomains || [];
                    if (!blacklistDomains.includes(domain)) {
                        blacklistDomains.push(domain);
                        chrome.storage.sync.set({
                            blacklistDomains: blacklistDomains
                        }, function() {
                            document.getElementById('blacklistDomainInput').value = '';
                            updateBlacklistDomainsList(blacklistDomains);
                        });
                    }
                });
            }
        }
    });
}

// 更新黑名单列表显示
function updateBlacklistDomainsList(blacklistDomains) {
    const tbody = document.querySelector('#blacklistTbody');
    let str = '';

    blacklistDomains.forEach(function (obj, i) {
        str += `
            <tr class="border-b hover:bg-gray-100 dark:hover:bg-gray-700 dark:hover:text-white">
                <td class="px-6 py-4">${i + 1}</td>
                <td class="px-6 py-4">${obj}</td>
                <td class="px-6 py-4">
                    <button url="${obj}" type="button" class="_js_del_blacklist inline-flex items-center px-3 py-1.5 text-sm font-medium text-red-600 hover:text-white bg-red-50 hover:bg-red-600 rounded-lg transition-colors duration-200">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        ${chrome.i18n.getMessage('delete')}
                    </button>
                </td>
            </tr>`;
    });
    tbody.innerHTML = str;

    // 添加删除按钮事件监听
    const delBtns = document.querySelectorAll('._js_del_blacklist');
    delBtns.forEach(function(btn) {
        btn.addEventListener('click', function (event) {
            // 获取URL，可能从button或其子元素获取
            let url = event.target.getAttribute('url');
            if (!url) {
                url = event.target.closest('button').getAttribute('url');
            }
            const updatedBlacklistDomains = blacklistDomains.filter(e => e !== url);
            chrome.storage.sync.set({ blacklistDomains: updatedBlacklistDomains }, function() {
                updateBlacklistDomainsList(updatedBlacklistDomains);
            });
        });
    });
}

// 可以添加一个视觉提示，在输入框旁显示Pro标记
function updateProBadge() {
    const domainInput = document.getElementById('domainInput');
    const addButton = document.getElementById('add');
    
    // 添加Pro标记和提示
    domainInput.setAttribute('title', chrome.i18n.getMessage('proFeatureHint') || 'Pro feature only');
    
    // 可以选择性地添加一个小图标或者标记
    const proBadge = document.createElement('span');
    proBadge.className = 'pro-badge text-xs absolute -top-2 -right-2';
    proBadge.textContent = 'PRO';
    addButton.parentElement.style.position = 'relative';
    addButton.parentElement.appendChild(proBadge);
}

// 更新白名单列表显示
function updateDomainsList(domains) {
    const tbody = document.querySelector('#tbody');
    let str = '';
    
    domains.forEach(function (obj, i) {
        str += `
            <tr class="border-b hover:bg-gray-100 dark:hover:bg-gray-700 dark:hover:text-white">
                <td class="px-6 py-4">${i + 1}</td>
                <td class="px-6 py-4">${obj}</td>
                <td class="px-6 py-4">
                    <button url="${obj}" type="button" class="_js_del inline-flex items-center px-3 py-1.5 text-sm font-medium text-red-600 hover:text-white bg-red-50 hover:bg-red-600 rounded-lg transition-colors duration-200">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        ${chrome.i18n.getMessage('delete')}
                    </button>
                </td>
            </tr>`;
    });
    tbody.innerHTML = str;

    // 添加删除按钮事件监听
    const delBtns = document.querySelectorAll('._js_del');
    delBtns.forEach(function(btn) {
        btn.addEventListener('click', function (event) {
            // 获取URL，可能从button或其子元素获取
            let url = event.target.getAttribute('url');
            if (!url) {
                url = event.target.closest('button').getAttribute('url');
            }
            const updatedDomains = domains.filter(e => e !== url);
            chrome.storage.sync.set({ domains: updatedDomains }, function() {
                updateDomainsList(updatedDomains);
            });
        });
    });
}

// 屏蔽强度按钮切换
function initStrengthButtons() {
    // cursor-help 鼠标悬停显示提示信息
    const blockStrengthHelp = document.getElementById('blockStrengthHelp');
    blockStrengthHelp.addEventListener('mouseenter', () => {
        blockStrengthHelp.nextElementSibling.classList.remove('invisible', 'opacity-0');
    });
    blockStrengthHelp.addEventListener('mouseleave', () => {
        blockStrengthHelp.nextElementSibling.classList.add('invisible', 'opacity-0');
    });

    const strengthButtons = document.querySelectorAll('[data-strength]');
    strengthButtons.forEach(button => {
        button.addEventListener('click', () => {
            // 如果点击强度高，需要验证是否是pro用户
            if (button.getAttribute('data-strength') === 'high') {
                chrome.storage.sync.get(['isPro'], function(result) {
                    if (!result.isPro) {
                        showModal('licenseModal');
                        return;
                    } else {
                        // 移除其他按钮的激活状态
                        strengthButtons.forEach(btn => {
                            btn.classList.remove('bg-violet-400', 'text-white', 'hover:bg-violet-500');
                            btn.classList.add('hover:bg-gray-200');
                        });
                        
                        // 添加当前按钮的激活状态
                        button.classList.remove('hover:bg-gray-200');
                        button.classList.add('bg-violet-400', 'text-white', 'hover:bg-violet-500');
            
                        // 更新屏蔽强度
                        chrome.storage.sync.set({ blockStrength: button.getAttribute('data-strength') }); 

                    }
                });
                return;
            }
            // 移除其他按钮的激活状态
            strengthButtons.forEach(btn => {
                btn.classList.remove('bg-violet-400', 'text-white', 'hover:bg-violet-500');
                btn.classList.add('hover:bg-gray-200');
            });
            
            // 添加当前按钮的激活状态
            button.classList.remove('hover:bg-gray-200');
            button.classList.add('bg-violet-400', 'text-white', 'hover:bg-violet-500');

            // 更新屏蔽强度
            chrome.storage.sync.set({ blockStrength: button.getAttribute('data-strength') });   
        });
    });
}

// pro相关逻辑
function initProStatus() {
    console.log('initProStatus');
    chrome.storage.sync.get(['isPro', 'email', 'expireDate'], function(result) {
        console.log('result', result.isPro,result.email,result.expireDate);
        const isPro = result.isPro || false;
        const proStatus = document.querySelector('.pro-status');
        proStatus.setAttribute('data-is-pro', isPro);
        if (isPro) {
            // show email
            document.getElementById('userEmail').textContent = result.email;
            // hide upgrade button
            document.getElementById('upgradePro').classList.add('hidden');
            // show expire date
            document.getElementById('proExpireDate').textContent = result.expireDate;
        } else {
            // hide email
            document.getElementById('userEmail').textContent = '';
        }
    });

    // 点击upgradePro，弹出验证pro license 弹窗
    document.getElementById('upgradePro').addEventListener('click', function() {
        showModal('licenseModal');
    });
    document.getElementById('closeLicenseModal').addEventListener('click', function() {
        hideModal('licenseModal');
    });

    // 点击开始验证，验证许可证
    document.getElementById('verifyLicense').addEventListener('click', function() {
        console.log('verifyLicense');
        verifyLicense().then(result => {
            if (result) {
                alert('verify success! you are a pro user now');
                // 隐藏弹窗
                hideModal('licenseModal');

            } else {
                alert('verify failed!');
                console.log('verify failed!');
            }
        });
    });

    document.getElementById('goToPro').addEventListener('click', function() {
        // showModal('subscribeModal');
        // 打开连接
        window.open('https://cameronchen.gumroad.com/l/frojl', '_blank');
    });

    // 判断激活码是否有效，是否是pro用户
    function verifyLicense() {
        // 测试用的激活码和产品ID
        const testLicenseKey = document.getElementById('licenseInput').value;//D6F3F923-908C4DCE-BBB60779-90810FAE
        const testProductId = "kKK1URrvN3VIF9RTE0ww_Q==";
        
        return fetch('https://api.gumroad.com/v2/licenses/verify', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `product_id=${testProductId}&license_key=${testLicenseKey}`
        })
        .then(response => response.json())
        .then(data => {
            // 验证成功且未退款/取消订阅
            if (data.success && 
                !data.purchase.refunded && 
                !data.purchase.chargebacked &&
                !data.purchase.subscription_ended_at && 
                !data.purchase.subscription_cancelled_at) {
                // 储存pro状态，邮箱
                chrome.storage.sync.set({ isPro: true, email: data.purchase.email, expireDate: data.purchase.recurrence });
                // 展示pro状态
                document.querySelector('.pro-status').setAttribute('data-is-pro', 'true');
                // 展示pro到期时间
                document.getElementById('proExpireDate').textContent = data.purchase.recurrence;
                // 展示email
                document.getElementById('userEmail').textContent = data.purchase.email;
                // hide upgrade button
                document.getElementById('upgradePro').classList.add('hidden');
                return true; // 是pro用户
            }
            return false; // 不是pro用户
        })
        .catch(error => {
            console.error('verify license failed:', error);
            return false;
        });
    }
}


// 显示弹窗
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.remove('hidden');
    modal.classList.add('flex');
}

// 隐藏弹窗
function hideModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.remove('flex');
    modal.classList.add('hidden');
}

// 显示许可证验证弹窗
function showLicenseModal() {
    if (window.location.hash === '#license') {
        showModal('licenseModal');
    }
}

// 添加一个函数来检查是否为Pro用户
async function isProUser() {
    // 这里添加实际的Pro用户验证逻辑
    try {
        const result = await chrome.storage.sync.get(['isPro']);
        return result.isPro === true;
    } catch (error) {
        console.error('Error checking pro status:', error);
        return false;
    }
}