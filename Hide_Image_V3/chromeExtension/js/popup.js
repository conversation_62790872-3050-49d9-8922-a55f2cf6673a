// Element selectors
const image = document.getElementById('image');
const enable = document.getElementById('enable');
const enableBlacklist = document.getElementById('enableBlacklist');
const whitelistModeBtn = document.getElementById('whitelistModeBtn');
const blacklistModeBtn = document.getElementById('blacklistModeBtn');
const whitelistOption = document.getElementById('whitelistOption');
const blacklistOption = document.getElementById('blacklistOption');
const listModeSection = document.getElementById('listModeSection');
const upgradeModal = document.getElementById('upgradeModal');
const upgradeBtn = document.getElementById('upgradeBtn');
const cancelUpgradeBtn = document.getElementById('cancelUpgradeBtn');


$(document).ready(function() {
  $('#moreBtn').off('click').on('click', function(event) {
    event.stopPropagation();
    $('#moreMenu').toggleClass('show');
  });

  $(document).on('click', function(event) {
    if (!$(event.target).closest('#moreMenu').length && !$(event.target).is('#moreBtn')) {
      $('#moreMenu').removeClass('show');
    }
  });
});

// 移除顶层的await
chrome.storage.local.get(['active', 'hide'], function(result) {
  let initialState = {
    active: result.active,
    hide: result.hide
  };

  syncUI();
});

image.addEventListener('change', () => {
  chrome.storage.local.get(['active'], function(result) {
    chrome.storage.local.set({ active: image.checked }, function() {
      syncUI();
      chrome.tabs.reload();
    });
  });
});

function syncUI() {
  chrome.storage.local.get(['active', 'hide'], function(result) {
    image.checked = result.active;

    // 根据无图模式状态显示/隐藏黑白名单选择
    if (result.active) {
      listModeSection.style.display = 'block';
      initListMode();
    } else {
      listModeSection.style.display = 'none';
    }

    // 初始化深色模式
    initDarkMode();
    // 屏蔽强度
    initBlockStrength();
    // 初始化更新日志
    initUpdateLog();
  });
}

// 初始化黑白名单模式
function initListMode() {
  chrome.storage.sync.get(['listMode', 'domains', 'blacklistDomains'], function(result) {
    const listMode = result.listMode || 'whitelist';

    // 更新按钮状态
    if (listMode === 'whitelist') {
      whitelistModeBtn.classList.add('bg-violet-400', 'text-white', 'hover:bg-violet-500');
      whitelistModeBtn.classList.remove('hover:bg-gray-200');
      blacklistModeBtn.classList.remove('bg-violet-400', 'text-white', 'hover:bg-violet-500');
      blacklistModeBtn.classList.add('hover:bg-gray-200');

      whitelistOption.classList.remove('hidden');
      blacklistOption.classList.add('hidden');
    } else {
      blacklistModeBtn.classList.add('bg-violet-400', 'text-white', 'hover:bg-violet-500');
      blacklistModeBtn.classList.remove('hover:bg-gray-200');
      whitelistModeBtn.classList.remove('bg-violet-400', 'text-white', 'hover:bg-violet-500');
      whitelistModeBtn.classList.add('hover:bg-gray-200');

      whitelistOption.classList.add('hidden');
      blacklistOption.classList.remove('hidden');
    }

    // 更新当前网站的状态
    chrome.tabs.query({ active: true, currentWindow: true }, function (tabs) {
      let currentHost = (new URL(tabs[0].url)).hostname;

      if (listMode === 'whitelist') {
        const domains = result.domains || [];
        enable.checked = domains.includes(currentHost);
      } else {
        const blacklistDomains = result.blacklistDomains || [];
        enableBlacklist.checked = blacklistDomains.includes(currentHost);
      }
    });
  });
}

// 白名单模式按钮事件
whitelistModeBtn.addEventListener('click', function() {
  chrome.storage.sync.set({ listMode: 'whitelist' }, function() {
    initListMode();
    chrome.tabs.reload();
  });
});

// 黑名单模式按钮事件
blacklistModeBtn.addEventListener('click', function() {
  chrome.storage.sync.set({ listMode: 'blacklist' }, function() {
    initListMode();
    chrome.tabs.reload();
  });
});

// 白名单开关事件
document.getElementById('enable').addEventListener('click', function () {
  chrome.tabs.query({ active: true, currentWindow: true }, function (tabs) {
    var currentHost = (new URL(tabs[0].url)).hostname;
    chrome.storage.sync.get(['domains'], function (result) {
      var domains = result.domains ? [...result.domains] : [];
      if (enable.checked) {
        if (!domains.includes(currentHost)) {
          domains.push(currentHost);
        }
      } else {
        domains = domains.filter(e => e !== currentHost);
      }
      chrome.storage.sync.set({ domains: domains }, function() {
        initListMode();
        chrome.tabs.reload();
      });
    });
  });
});

// 黑名单开关事件
document.getElementById('enableBlacklist').addEventListener('click', function () {
  chrome.tabs.query({ active: true, currentWindow: true }, function (tabs) {
    var currentHost = (new URL(tabs[0].url)).hostname;
    chrome.storage.sync.get(['blacklistDomains'], function (result) {
      var blacklistDomains = result.blacklistDomains ? [...result.blacklistDomains] : [];
      if (enableBlacklist.checked) {
        if (!blacklistDomains.includes(currentHost)) {
          blacklistDomains.push(currentHost);
        }
      } else {
        blacklistDomains = blacklistDomains.filter(e => e !== currentHost);
      }
      chrome.storage.sync.set({ blacklistDomains: blacklistDomains }, function() {
        initListMode();
        chrome.tabs.reload();
      });
    });
  });
});

document.getElementById('exclude').addEventListener("click", function (){
  window.open(chrome.runtime.getURL('options.html'));
}); // 绑定列表事件

// 星级评分系统
const starRating = document.querySelector('.star-rating');
const stars = starRating.querySelectorAll('.star');
const ratingEmoji = document.querySelector('.rating-emoji');
const emojiSpan = ratingEmoji.querySelector('.emoji');

// 定义表情映射
const ratingEmojis = {
  1: { emoji: '😢', title: 'Very Dissatisfied' },
  2: { emoji: '😕', title: 'Dissatisfied' },
  3: { emoji: '😊', title: 'Neutral' },
  4: { emoji: '😄', title: 'Satisfied' },
  5: { emoji: '🤩', title: 'Very Satisfied' }
};

stars.forEach(star => {
  star.addEventListener('mouseover', () => {
    const rating = star.getAttribute('data-rating');
    highlightStars(rating);
    showEmoji(rating);
  });

  star.addEventListener('mouseout', () => {
    resetStars();
    hideEmoji();
  });

  star.addEventListener('click', () => {
    const rating = star.getAttribute('data-rating');
    // 跳转到 Chrome 网上应用店的评分页面
    chrome.tabs.create({ url: `https://chromewebstore.google.com/detail/lnemmogegmgllangfmlpclaomcknfnbp/reviews` });
  });
});

function highlightStars(rating) {
  stars.forEach(star => {
    if (star.getAttribute('data-rating') <= rating) {
      star.classList.add('active');
    } else {
      star.classList.remove('active');
    }
  });
}

function resetStars() {
  stars.forEach(star => star.classList.remove('active'));
}

function showEmoji(rating) {
  const { emoji, title } = ratingEmojis[rating];
  emojiSpan.textContent = emoji;
  ratingEmoji.title = title;
  ratingEmoji.classList.add('show');
}

function hideEmoji() {
  ratingEmoji.classList.remove('show');
}

// init Block Strength
function initBlockStrength() {
  const strengthBtns = document.querySelectorAll('[data-strength]');
  
  // 移除所有按钮的高亮样式
  strengthBtns.forEach(btn => {
    btn.classList.remove('bg-violet-400', 'text-white', 'hover:bg-violet-500');
    btn.classList.add('hover:bg-gray-200');
  });

  // 展示对应强度按钮
  chrome.storage.sync.get(['blockStrength'], function(result) {
    console.log('result', result.blockStrength);
    const blockStrength = result.blockStrength || 'medium';
    const strengthButtons = document.querySelectorAll('[data-strength]');
    strengthButtons.forEach(button => {
        if (button.getAttribute('data-strength') === blockStrength) {
            button.click();
        }
    });
  });
  
  strengthBtns.forEach(button => {
      button.addEventListener('click', () => {
          // 如果点击强度高，需要验证是否是pro用户
          if (button.getAttribute('data-strength') === 'high') {
              chrome.storage.sync.get(['isPro'], function(result) {
                  if (!result.isPro) {
                      // 显示模态框
                      upgradeModal.classList.remove('hidden');
                      return;
                  } else {
                    // 移除其他按钮的激活状态
                    strengthBtns.forEach(btn => {
                      btn.classList.remove('bg-violet-400', 'text-white', 'hover:bg-violet-500');
                      btn.classList.add('hover:bg-gray-200');
                    });

                    // 添加当前按钮的激活状态
                    button.classList.remove('hover:bg-gray-200');
                    button.classList.add('bg-violet-400', 'text-white', 'hover:bg-violet-500');

                    // 更新屏蔽强度
                    console.log('button.getAttribute(\'data-strength\')', button.getAttribute('data-strength'));
                    chrome.storage.sync.set({ blockStrength: button.getAttribute('data-strength') }); 
                  }
              });
          } else {
            // 移除其他按钮的激活状态
            strengthBtns.forEach(btn => {
                btn.classList.remove('bg-violet-400', 'text-white', 'hover:bg-violet-500');
                btn.classList.add('hover:bg-gray-200');
            });
            
            // 添加当前按钮的激活状态
            button.classList.remove('hover:bg-gray-200');
            button.classList.add('bg-violet-400', 'text-white', 'hover:bg-violet-500');

            // 更新屏蔽强度
            console.log('button.getAttribute(\'data-strength\')', button.getAttribute('data-strength'));
            chrome.storage.sync.set({ blockStrength: button.getAttribute('data-strength') });  
          } 
      });
  });
}

// init Dark mode
function initDarkMode() {
  chrome.storage.sync.get(['darkMode'], function(result) {
    if (result.darkMode) {
      document.body.classList.add('dark');
    } else {
      document.body.classList.remove('dark');
    }
  });
}

// 添加模态框按钮事件处理
upgradeBtn.addEventListener('click', () => {
    // 这里添加跳转到升级页面的逻辑
    chrome.tabs.create({ url: 'https://cameronchen.gumroad.com/l/frojl' });
    upgradeModal.classList.add('hidden');
});

cancelUpgradeBtn.addEventListener('click', () => {
    upgradeModal.classList.add('hidden');
});

// 点击模态框外部关闭
upgradeModal.addEventListener('click', (e) => {
    if (e.target === upgradeModal) {
        upgradeModal.classList.add('hidden');
    }
});

// 添加验证许可证按钮事件处理
document.getElementById('verifyLicenseBtn').addEventListener('click', () => {
  // 打开options页面，打开后自动打开许可证验证弹窗
  window.open(chrome.runtime.getURL('options.html') + '#license');
});

function verifyLicense(licenseKey) {
    // 这里添加实际的许可证验证逻辑
    // 示例：
    chrome.storage.sync.set({ 
        isPro: true,
        licenseKey: licenseKey 
    }, function() {
        upgradeModal.classList.add('hidden');
        // 可以添加一个成功提示
        alert('许可证验证成功！');
    });
}


function initUpdateLog() {
  // 检查更新日志是否需要显示
  chrome.storage.local.get(['closedUpdateVersion'], function(result) {
    const closedVersion = result.closedUpdateVersion || '';
    const updateLogDiv = document.querySelector('[update-log-version]');
    
    if (updateLogDiv) {
      const currentVersion = updateLogDiv.getAttribute('update-log-version');
      if (closedVersion === currentVersion) {
        updateLogDiv.style.display = 'none';
      }
    }
  });

  // 关闭更新日志的点击事件
  const closeUpdateLogBtn = document.getElementById('closeUpdateLog');
  if (closeUpdateLogBtn) {
    closeUpdateLogBtn.addEventListener('click', function() {
      const version = this.getAttribute('version');
      // 存储已关闭的版本号
      chrome.storage.local.set({ closedUpdateVersion: version }, function() {
        // 隐藏更新日志
        const updateLogDiv = document.querySelector('[update-log-version]');
        if (updateLogDiv) {
          updateLogDiv.style.display = 'none';
        }
      });
    });
  }
}