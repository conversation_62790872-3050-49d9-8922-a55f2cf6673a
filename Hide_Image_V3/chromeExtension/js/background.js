chrome.runtime.onInstalled.addListener(function(details) {
  if (details.reason === "install") {
    chrome.storage.local.set({
      active: true,
      hide: 'HIDE'
    });
    chrome.tabs.create({ url: "https://xzonesoft.com/hide_images/welcome.html" });
  }
});

chrome.commands.onCommand.addListener(async (command) => {
  if (command === "hide_command") {
    const { active } = await chrome.storage.local.get();
    if (active) await chrome.storage.local.set({ active: !active });
    else await chrome.storage.local.set({ active: !active });
    chrome.tabs.reload();
  }
});