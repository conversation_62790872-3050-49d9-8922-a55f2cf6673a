<!DOCTYPE html>
<html>

    <head>
        <title>__MSG_setPageTitle__</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <link rel="stylesheet" type="text/css" href="css/style.css" />
        <link rel="stylesheet" href="css/tailwind.min.css">
    </head>

<body class="exclude bg-gray-100">
    <div class="flex min-h-screen">
        <!-- 左侧菜单 -->
        <div class="w-64 bg-white shadow-lg relative h-screen">
            <!-- 菜单内容区域 -->
            <div class="p-4">
                <h1 class="text-xl font-bold mb-8" style="max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">__MSG_appName__</h1>
                <nav class="space-y-2">
                    <a href="#whitelist" class="menu-item active block py-3 px-4 rounded-lg hover:bg-gray-100 transition-colors duration-200 text-base">__MSG_whiteList__</a>
                    <a href="#settings" class="menu-item block py-3 px-4 rounded-lg hover:bg-gray-100 transition-colors duration-200 text-base">__MSG_Settings__</a>
                    <a href="#tools" class="menu-item block py-3 px-4 rounded-lg hover:bg-gray-100 transition-colors duration-200 text-base">__MSG_more_tools__</a>
                    <a href="https://xzonesoft.com/hide_images/price.html" target="_blank" rel="noopener noreferrer" class="block py-3 px-4 rounded-lg hover:bg-gray-100 transition-colors duration-200 text-base" style="background: linear-gradient(to right, #f59e0b, #ea580c);color: #fff;">
                        <svg xmlns="http://www.w3.org/2000/svg" class="inline-block w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 1l3 6h6l-5 4 2 6-5-4-5 4 2-6-5-4h6z"/>
                        </svg>
                        __MSG_upgradeToPro__
                    </a>
                </nav>
            </div>

            <!-- PRO状态显示 - 使用fixed定位固定在底部 -->
            <div class="absolute bottom-0 left-0 right-0 p-4 border-t bg-white">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-4">
                            <div class="pro-status flex items-start space-x-2" data-is-pro="false">
                                <span class="pro-btn">PRO</span>
                                <span id="proExpireDate" class="text-black">9999-99-99</span>
                            </div>
                            <div class="text-xs text-gray-500 mt-1" id="userEmail">__MSG_userEmail__</div>
                        </div>
                    </div>
                    <a href="#" class="upgrade-pro-btn" id="upgradePro">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd" />
                        </svg>
                        __MSG_upgradeToPro__
                    </a>
                </div>
            </div>
        </div>

        <!-- 右侧内容区 -->
        <div class="flex-1 p-8">
            <!-- 白名单页面 -->
            <div id="whitelist" class="page active">
                <h2 class="text-2xl font-bold mb-6">__MSG_whiteList__</h2>
                <div class="bg-white rounded-lg shadow p-6">
                    <!-- 将添加白名单表单移到这里 -->
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold mb-4">__MSG_addWhiteList__</h3>
                        <div class="flex gap-4">
                            <input id="domainInput" type="text" placeholder="eg: google.com" 
                                class="flex-1 px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <button id="add" class="px-6 py-2 bg-violet-400 text-white rounded-lg hover:bg-violet-500">
                                __MSG_Add__
                            </button>
                        </div>
                    </div>

                    <!-- 白名单表格 -->
                    <div class="overflow-auto" style="max-height: calc(100vh - 280px);">
                        <table class="w-full table-auto">
                            <thead class="bg-gray-50 sticky top-0">
                                <tr>
                                    <th class="px-6 py-3 text-left">__MSG_appName__</th>
                                    <th class="px-6 py-3 text-left">__MSG_Domain__</th>
                                    <th class="px-6 py-3 text-left">__MSG_Operation__</th>
                                </tr>
                            </thead>
                            <tbody id="tbody" class="[&>tr:hover]:text-gray-900 [&>tr:hover]:bg-gray-50"></tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 设置页面 -->
            <div id="settings" class="page hidden">
                <h2 class="text-2xl font-bold mb-6">__MSG_Settings__</h2>
                <div class="bg-white rounded-lg shadow p-6">
                    <!-- 黑暗模式设置 -->
                    <div class="flex items-center justify-between py-4 border-b">
                        <div>
                            <h3 class="text-lg font-medium">__MSG_darkMode__</h3>
                            <p class="text-gray-500 text-sm">__MSG_darkModeDesc__</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="darkModeToggle" class="sr-only">
                            <div class="switch-toggle"></div>
                        </label>
                    </div>

                    <!-- 屏蔽强度 -->
                    <div class="flex items-center justify-between py-4 border-b">
                        <div>
                            <h3 class="text-lg font-medium flex items-center">
                                __MSG_blockStrength__
                                <span class="ml-2 relative group cursor-help">
                                    <svg id="blockStrengthHelp" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                    </svg>
                                    <div class="absolute left-full ml-2 w-72 bg-black text-white text-sm rounded-lg py-3 px-4 invisible opacity-0 group-hover:visible group-hover:opacity-100 transition-opacity duration-200 whitespace-pre-line leading-relaxed">
                                        __MSG_blockStrengthTip__
                                    </div>
                                </span>
                            </h3>
                            <p class="text-gray-500 text-sm">__MSG_blockStrengthDesc__</p>
                        </div>
                        <div class="inline-flex bg-gray-100 rounded-lg overflow-hidden">
                            <button class="w-24 py-2.5 text-sm hover:bg-gray-200 transition-colors" data-strength="low">
                                __MSG_blockStrengthLow__
                            </button>
                            <button class="w-24 py-2.5 text-sm bg-violet-400 text-white hover:bg-violet-500 transition-colors" data-strength="medium">
                                __MSG_blockStrengthMedium__
                            </button>
                            <button class="relative w-24 py-2.5 text-sm hover:bg-gray-200 transition-colors group" data-strength="high">
                                <span class="pro-badge">VIP</span>
                                __MSG_blockStrengthHigh__
                            </button>
                        </div>
                    </div>

                    <!-- 语言设置 -->
                    <div class="flex items-center justify-between py-4 border-b">
                        <div>
                            <h3 class="text-lg font-medium">__MSG_language__</h3>
                            <p class="text-gray-500 text-sm">__MSG_languageDesc__</p>
                        </div>
                        <select id="languageSelect" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-violet-400 focus:border-violet-400 block p-2.5">
                            <option value="am">አርኛ</option>
                            <option value="ar">العربية</option>
                            <option value="bg">Български</option>
                            <option value="bn">বাংলা</option>
                            <option value="ca">Català</option>
                            <option value="cs">Čeština</option>
                            <option value="da">Dansk</option>
                            <option value="de">Deutsch</option>
                            <option value="el">Ελληνικά</option>
                            <option value="en" selected>English</option>
                            <option value="en_GB">English (UK)</option>
                            <option value="en_US">English (US)</option>
                            <option value="es">Español</option>
                            <option value="es_419">Español (Latinoamérica)</option>
                            <option value="et">Eesti</option>
                            <option value="fa">فارسی</option>
                            <option value="fi">Suomi</option>
                            <option value="fil">Filipino</option>
                            <option value="fr">Français</option>
                            <option value="gu">ગજરાતી</option>
                            <option value="he">עברית</option>
                            <option value="hi">हिन्दी</option>
                            <option value="hr">Hrvatski</option>
                            <option value="hu">Magyar</option>
                            <option value="id">Bahasa Indonesia</option>
                            <option value="it">Italiano</option>
                            <option value="ja">日本語</option>
                            <option value="ko">한국어</option>
                            <option value="lt">Lietuvių</option>
                            <option value="lv">Latviešu</option>
                            <option value="ml">മലയാളം</option>
                            <option value="mr">मराठी</option>
                            <option value="ms">Bahasa Melayu</option>
                            <option value="nl">Nederlands</option>
                            <option value="no">Norsk</option>
                            <option value="pl">Polski</option>
                            <option value="pt_BR">Português (Brasil)</option>
                            <option value="pt_PT">Português (Portugal)</option>
                            <option value="ro">Română</option>
                            <option value="ru">Русский</option>
                            <option value="sk">Slovenčina</option>
                            <option value="sl">Slovenščina</option>
                            <option value="sr">Српски</option>
                            <option value="sv">Svenska</option>
                            <option value="sw">Kiswahili</option>
                            <option value="ta">தமிழ்</option>
                            <option value="te">తెలుగు</option>
                            <option value="th">ทย</option>
                            <option value="tr">Türkçe</option>
                            <option value="uk">Українська</option>
                            <option value="ur">اردو</option>
                            <option value="vi">Tiếng Việt</option>
                            <option value="zh_CN">简体中文</option>
                            <option value="zh_TW">繁體中文</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 工具页面 -->
            <div id="tools" class="page hidden">
                <h2 class="text-2xl font-bold mb-6">__MSG_more_tools__</h2>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- world clock -->
                        <div class="border rounded-lg p-4 hover:shadow-lg transition-shadow">
                            <div class="flex items-start space-x-4">
                                <img src="img/icons/plugin3.png" alt="World Clock" class="w-16 h-16 rounded-xl">
                                <div class="flex-1">
                                    <h3 class="font-semibold text-lg mb-1">World Clock</h3>
                                    <p class="text-gray-600 text-sm mb-2 app-description">Time zone conversion and world clock display, support for one-time display and conversion of multiple locations/time zones</p>
                                    <div class="flex flex-wrap gap-2">
                                        <a href="https://chromewebstore.google.com/detail/hkhggnncdpfibdhinjiegagmopldibha" 
                                           class="inline-flex items-center justify-center w-28 px-3 py-1.5 bg-gray-100 hover:bg-gray-200 rounded-full text-sm" 
                                           target="_blank">
                                            <img src="img/icons/chrome.png" alt="Chrome" class="w-4 h-4 mr-1.5">
                                            <span>Chrome</span>
                                        </a>
                                        <a href="https://microsoftedge.microsoft.com/addons/detail/fjdnmmpkbhphdnnfnmjilmmaambppkml" 
                                           class="inline-flex items-center justify-center w-28 px-3 py-1.5 bg-gray-100 hover:bg-gray-200 rounded-full text-sm"
                                           target="_blank">
                                            <img src="img/icons/edge.png" alt="Edge" class="w-4 h-4 mr-1.5">
                                            <span>Edge</span>
                                        </a>
                                        <a href="https://addons.mozilla.org/addon/world-clock-timezone-converter/" 
                                           class="inline-flex items-center justify-center w-28 px-3 py-1.5 bg-gray-100 hover:bg-gray-200 rounded-full text-sm"
                                           target="_blank">
                                            <img src="img/icons/firefox.png" alt="Firefox" class="w-4 h-4 mr-1.5">
                                            <span>Firefox</span>
                                        </a>
                                        <a href="https://www.u.tools/plugins/detail/%E4%B8%96%E7%95%8C%E6%97%B6%E9%92%9F%20-%20%E6%97%B6%E5%8C%BA%E8%BD%AC%E6%8D%A2%E5%99%A8/" 
                                           class="inline-flex items-center justify-center w-28 px-3 py-1.5 bg-gray-100 hover:bg-gray-200 rounded-full text-sm"
                                           target="_blank">
                                            <img src="img/icons/utools.png" alt="uTools" class="w-4 h-4 mr-1.5">
                                            <span>uTools</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 应用卡片 2 -->
                        <div class="border rounded-lg p-4 hover:shadow-lg transition-shadow">
                            <div class="flex items-start space-x-4">
                                <img src="img/icons/plugin2.png" alt="Screenshot Tool" class="w-16 h-16 rounded-xl">
                                <div class="flex-1">
                                    <h3 class="font-semibold text-lg mb-1">Unix Timestamp Converter</h3>
                                    <p class="text-gray-600 text-sm mb-2 app-description">Supports current Unixtimestamp acquisition, timestamp conversion, timestamp to time, timestamp to date, supports millisecond.</p>
                                    <div class="flex flex-wrap gap-2">
                                        <a href="https://apps.apple.com/app/id6737517840" 
                                           class="inline-flex items-center justify-center w-28 px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-full text-sm">
                                            <img src="img/icons/app-store.png" alt="App Store" class="w-4 h-4 mr-1">
                                            <span>iOS App</span>
                                        </a>
                                        <a href="https://apps.apple.com/app/id6737517840" 
                                           class="inline-flex items-center justify-center w-28 px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-full text-sm">
                                            <img src="img/icons/app-store.png" alt="Mac" class="w-4 h-4 mr-1">
                                            <span>Mac App</span>
                                        </a>
                                        <a href="https://chromewebstore.google.com/detail/pgfceholljknjpmpnmndmjknekgcjeba" 
                                           class="inline-flex items-center justify-center w-28 px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-full text-sm">
                                            <img src="img/icons/chrome.png" alt="Chrome" class="w-4 h-4 mr-1">
                                            <span>Chrome</span>
                                        </a>
                                        <a href="https://microsoftedge.microsoft.com/addons/detail/jigdcejfajfaokcpnbakekgnlkddaigj" 
                                           class="inline-flex items-center justify-center w-28 px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-full text-sm">
                                            <img src="img/icons/edge.png" alt="Edge" class="w-4 h-4 mr-1">
                                            <span>Edge</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 应用卡片 3 -->
                        <div class="border rounded-lg p-4 hover:shadow-lg transition-shadow">
                            <div class="flex items-start space-x-4">
                                <img src="img/icons/plugin8.png" alt="Extension Manager" class="w-16 h-16 rounded-xl">
                                <div class="flex-1">
                                    <h3 class="font-semibold text-lg mb-1">Extension Manager</h3>
                                    <p class="text-gray-600 text-sm mb-2 app-description">Manage your Chrome extensions with ease. Install, uninstall, and update extensions with a single click.</p>
                                    <div class="flex flex-wrap gap-2">
                                        <a href="https://chromewebstore.google.com/detail/njpmnmlcmpmkbnejkfdfhngkdecefpdp" class="inline-flex items-center justify-center w-28 px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-full text-sm">
                                            <img src="img/icons/chrome.png" alt="Chrome" class="w-4 h-4 mr-1">
                                            <span>Chrome</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 验证pro license 弹窗-->
        <div id="licenseModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center">
            <div class="bg-white rounded-lg w-96 relative">
                <div class="p-6">
                    <h2 class="text-xl font-bold mb-3">__MSG_verifyLicense__</h2>
                    <p class="text-gray-600 mb-4 text-sm">__MSG_checkEmailForLicense__</p>
                    
                    <div class="space-y-3">
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="w-5 h-5 text-gray-400" viewBox="0 0 24 24">
                                    <path fill="currentColor" d="M10 13v3h3v3h3v2l2 2h5v-4L12.74 8.74C12.91 8.19 13 7.6 13 7c0-3.31-2.69-6-6-6S1 3.69 1 7a6.005 6.005 0 0 0 8.47 5.47L10 13ZM6 7a1 1 0 1 1 0-2a1 1 0 0 1 0 2Z"/>
                                </svg>
                            </div>
                            <input type="text" 
                                id="licenseInput"
                                class="w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-violet-400 focus:border-violet-400" 
                                placeholder="xxxxxxxx-xxx...">
                        </div>
                        
                        <button id="verifyLicense" class="w-full bg-violet-400 text-white py-2 rounded-lg hover:bg-violet-500 transition-colors">
                            __MSG_startVerification__
                        </button>
                    </div>

                    <div class="mt-6 flex items-center justify-between text-sm">
                        <a href="https://xzonesoft.com" 
                           class="text-gray-600 hover:text-gray-900 transition-colors" 
                           target="_blank">
                            __MSG_contactUs__
                        </a>
                        
                        <button id="goToPro" 
                           class="inline-flex items-center text-violet-400 hover:text-violet-500 transition-colors" 
                           target="_blank">
                            __MSG_upgradeToPro__
                            <svg class="w-4 h-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            </svg>
                        </button>
                    </div>
                </div>
                
                <button class="absolute top-4 right-4 text-gray-400 hover:text-gray-600" id="closeLicenseModal">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>

        <!-- 订阅弹窗，嵌入iframe -->
        <!-- <div id="subscribeModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center w-96">
            <iframe src="https://cameronchen.gumroad.com/l/frojl" class="w-full h-full"></iframe>
        </div> -->
    </div>
    <script src="js/i18n.js"></script>
    <script src="js/options.js"></script>
</body>

</html>